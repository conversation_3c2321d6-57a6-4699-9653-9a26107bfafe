/**
 * API配置文件
 * 管理所有API相关的配置和请求方法
 */

const apiConfig = {
  // ==================== 基础配置 ====================
  
  // API基础URL
  baseUrl: 'https://adminfrontend.welshine.com/wxapp/api',
  
  // 默认请求头
  defaultHeaders: {
    'content-type': 'application/json'
  },
  
  // 默认超时时间（毫秒）
  timeout: 10000,
  
  // ==================== 接口地址 ====================
  
  endpoints: {
    // 检查打印机设备
    checkPrinterDevice: '/PrinterManager/CheckPrinterDevice'
  },
  
  // ==================== 默认参数 ====================

  // 默认应用ID
  defaultAppId: 1,

  // 默认设备型号
  defaultModel: 'P2',

  // ==================== 错误码定义 ====================

  errorCodes: {
    // 系统级错误
    SERIALIZE_FAILED: -2,           // 序列化转换出错
    VALIDATION_FAILED: -1,         // 参数格式错误-特性校验
    NONE: 0,                       // None
    UNAUTHORIZED: 401,             // 鉴权失败
    FORBIDDEN: 403,                // 权限不足
    ERROR: 999,                    // 系统繁忙，请稍后再试
    PARAM: 1000,                   // 参数错误

    // 打印机相关错误
    PRINTER_NOT_EXIST: 9000,       // 设备不存在
    PRINTER_MODEL_NOT_EMPTY: 9001, // 打印机型号不能为空
    PRINTER_SN_NOT_EMPTY: 9002     // 设备SN码不能为空
  },

  // 错误消息映射
  errorMessages: {
    [-2]: '序列化转换出错',
    [-1]: '参数格式错误',
    [0]: 'None',
    [401]: '鉴权失败',
    [403]: '权限不足',
    [999]: '系统繁忙，请稍后再试',
    [1000]: '参数错误',
    [9000]: '设备不存在',
    [9001]: '打印机型号不能为空',
    [9002]: '设备SN码不能为空'
  }
}

/**
 * 生成请求ID
 */
function generateRequestId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 获取用户ID
 */
function getUserId() {
  return wx.getStorageSync('uid') || 'anonymous_user'
}

/**
 * 构造标准请求头
 */
function buildRequestHead(customParams = {}) {
  return {
    requestId: generateRequestId(),
    uid: getUserId(),
    appId: apiConfig.defaultAppId,
    ...customParams
  }
}

/**
 * 获取错误消息
 * @param {string|number} code - 错误码
 * @returns {string} 错误消息
 */
function getErrorMessage(code) {
  const numCode = parseInt(code)
  return apiConfig.errorMessages[numCode] || `未知错误 (${code})`
}

/**
 * 检查打印机设备API
 * @param {Object} device - 设备信息
 * @param {string} device.name - 设备名称/序列号
 * @param {string} device.deviceId - 设备ID
 * @param {string} model - 设备型号，默认为T50PRO
 * @returns {Promise} 返回检查结果
 */
function checkPrinterDevice(device, model = apiConfig.defaultModel) {
  return new Promise((resolve, reject) => {
    // 参数验证
    const sn = device.name || device.deviceId
    if (!sn) {
      resolve({
        success: false,
        message: getErrorMessage(apiConfig.errorCodes.PRINTER_SN_NOT_EMPTY),
        code: apiConfig.errorCodes.PRINTER_SN_NOT_EMPTY
      })
      return
    }

    if (!model) {
      resolve({
        success: false,
        message: getErrorMessage(apiConfig.errorCodes.PRINTER_MODEL_NOT_EMPTY),
        code: apiConfig.errorCodes.PRINTER_MODEL_NOT_EMPTY
      })
      return
    }

    const requestData = {
      head: buildRequestHead(),
      body: {
        model: model,
        sn: sn
      }
    }

    console.log('检查打印机设备请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.checkPrinterDevice,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('设备检查响应:', res)

        if (res.statusCode === 200 && res.data) {
          const responseCode = res.data.head?.code

          if (responseCode === '200') {
            // 设备检查成功
            resolve({
              success: true,
              isInStock: res.data.result?.isInStock || false,
              deviceInfo: res.data.result,
              message: res.data.head?.message || '查询成功'
            })
          } else {
            // 根据错误码返回具体错误信息
            const errorCode = parseInt(responseCode)
            const errorMessage = getErrorMessage(errorCode)

            resolve({
              success: false,
              message: errorMessage,
              code: errorCode,
              originalMessage: res.data.head?.message
            })
          }
        } else {
          reject(new Error(`网络请求失败 (HTTP ${res.statusCode})`))
        }
      },
      fail: (error) => {
        console.error('设备检查请求失败:', error)
        reject(error)
      }
    })
  })
}

module.exports = {
  apiConfig,
  generateRequestId,
  getUserId,
  buildRequestHead,
  getErrorMessage,
  checkPrinterDevice
}
